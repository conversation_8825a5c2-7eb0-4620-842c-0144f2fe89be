#!/usr/bin/env python
# encoding:utf-8

"""
奇安信天眼告警服务 MCP Server
基于 Model Context Protocol (MCP) 的奇安信天眼告警查询工具集成
提供2个主要功能：
1. 查询指定时间范围内的告警信息
2. 获取指定告警的详细信息
"""

from mcp.server.fastmcp import FastMCP
import logging
import requests
import time
import datetime
import random
import re
import base64
from typing import Dict, Optional, Any, List
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 尝试导入验证码识别库
try:
    import ddddocr
    HAS_DDDDOCR = True
except ImportError:
    HAS_DDDDOCR = False
    logger.warning("ddddocr模块未安装，验证码识别功能将不可用")

# 创建 FastMCP 实例
mcp = FastMCP("奇安信天眼告警服务")


class QianXinLoginSimulator:
    """奇安信天眼登录模拟器"""
    
    def __init__(self, base_url):
        """
        初始化登录模拟器
        
        Args:
            base_url (str): 服务器地址
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.csrf_token = None
        self.session_cookie = None
        
        # 禁用SSL证书验证
        self.session.verify = False
        
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document'
        })
        
        # 初始化验证码识别器
        if HAS_DDDDOCR:
            self.ocr = ddddocr.DdddOcr()
        else:
            self.ocr = None

    def get_login_page(self):
        """获取登录页面"""
        try:
            url = f"{self.base_url}/sensor/login"
            response = self.session.get(url, verify=False)
            
            # 从响应中提取csrf_token
            csrf_match = re.search(r'content="([a-f0-9]{32})"', response.text)
            if csrf_match:
                self.csrf_token = csrf_match.group(1)
                logger.info(f"获取到 CSRF Token: {self.csrf_token}")
            
            # 获取session cookie
            if 'session-sensor' in response.cookies:
                self.session_cookie = response.cookies['session-sensor']
                logger.info(f"获取到 Session Cookie: {self.session_cookie}")
            
            return response.status_code == 200
        except Exception as e:
            logger.error(f"获取登录页面失败: {e}")
            return False

    def get_captcha(self):
        """获取验证码"""
        try:
            timestamp = int(time.time() * 1000)
            url = f"{self.base_url}/skyeye/admin/captcha?r={random.random()}&t={timestamp}"
            
            headers = {
                'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Dest': 'image',
                'Referer': f'{self.base_url}/sensor/login'
            }
            
            response = self.session.get(url, headers=headers, verify=False)
            
            if response.status_code == 200:
                return response.content
            return None
        except Exception as e:
            logger.error(f"获取验证码失败: {e}")
            return None

    def recognize_captcha(self, image_data):
        """识别验证码"""
        try:
            if self.ocr and image_data:
                result = self.ocr.classification(image_data)
                logger.info(f"验证码识别结果: {result}")
                return result
            else:
                # 简单的验证码识别方法（返回固定值）
                result = "1234"  # 默认验证码
                logger.warning(f"使用默认验证码: {result}")
                return result
        except Exception as e:
            logger.error(f"验证码识别失败: {e}")
            return "1234"

    def encrypt_password(self):
        """加密密码（这里简化处理）"""
        return "U2FsdGVkX18woJynzevdp9AF+c6KpWiR0H+dfkYJfDo="

    def login(self, username, password, auto_recognize=True, verbose=False, max_retries=5):
        """
        带重试机制的登录函数
        
        Args:
            username (str): 用户名
            password (str): 密码
            auto_recognize (bool): 是否自动识别验证码
            verbose (bool): 是否显示详细日志
            max_retries (int): 最大重试次数
            
        Returns:
            bool: 登录是否成功
        """
        for attempt in range(max_retries):
            try:
                if verbose:
                    logger.info(f"第 {attempt + 1} 次尝试登录...")
                
                # 获取登录页面
                if not self.get_login_page():
                    continue
                
                # 获取验证码
                captcha_image = self.get_captcha()
                if not captcha_image:
                    continue
                
                # 识别验证码
                captcha_code = self.recognize_captcha(captcha_image)
                if not captcha_code:
                    continue
                
                # 执行登录
                success = self.perform_login(username, password, captcha_code)
                if success:
                    if verbose:
                        logger.info("登录成功!")
                    return True
                
                if attempt < max_retries - 1:
                    time.sleep(2)
                    
            except Exception as e:
                logger.error(f"登录过程发生异常: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        
        logger.error("登录失败，已达到最大重试次数")
        return False

    def perform_login(self, username, password, captcha):
        """执行登录"""
        try:
            # 加密密码
            encrypted_password = self.encrypt_password()
            
            url = f"{self.base_url}/skyeye/admin/login"
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/json',
                'Origin': self.base_url,
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': f'{self.base_url}/sensor/login'
            }
            
            login_data = {
                "type_login": "sys",
                "username": username,
                "password": encrypted_password,
                "authcode": captcha,
                "csrf_token": self.csrf_token,
                "r": random.random()
            }
            
            response = self.session.post(url, headers=headers, json=login_data, verify=False)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 200:
                    logger.info("登录成功!")
                    return True
                else:
                    logger.error(f"登录失败: {result.get('message', 'Unknown error')}")
                    return False
            return False
        except Exception as e:
            logger.error(f"执行登录失败: {e}")
            return False

    def get_session(self):
        """获取已登录的session"""
        return self.session


class AlertQueryManager:
    """告警查询管理器"""

    def __init__(self, login_simulator):
        """
        初始化查询管理器

        Args:
            login_simulator (QianXinLoginSimulator): 已登录的模拟器实例
        """
        self.simulator = login_simulator
        self.session = login_simulator.get_session()
        self.base_url = login_simulator.base_url

    def get_alerts_by_minutes_range(self, minutes_ago=5, limit=50):
        """
        根据时间范围查询告警

        Args:
            minutes_ago (int): 查询多少分钟前的告警
            limit (int): 返回数量限制

        Returns:
            dict: 告警数据
        """
        try:
            # 计算时间范围
            end_time = int(time.time())
            start_time = end_time - (minutes_ago * 60)

            # 构建查询参数
            params = {
                'offset': 1,
                'limit': limit,
                'start_time': start_time,
                'end_time': end_time,
                'order': 'desc'
            }

            # 发送查询请求
            url = f"{self.base_url}/skyeye/admin/alert/list"
            response = self.session.get(url, params=params, verify=False)

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 200:
                    return data.get('data', {})
                else:
                    logger.error(f"查询告警失败: {data.get('message', 'Unknown error')}")
                    return None
            else:
                logger.error(f"查询告警请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"查询告警异常: {e}")
            return None


class AlertDetailManager:
    """告警详情管理器"""

    def __init__(self, login_simulator):
        """
        初始化告警详情管理器

        Args:
            login_simulator (QianXinLoginSimulator): 已登录的模拟器实例
        """
        self.simulator = login_simulator
        self.session = login_simulator.get_session()
        self.base_url = login_simulator.base_url

    def get_alert_detail(self, alert_id):
        """
        获取告警详细信息

        Args:
            alert_id (str): 告警ID

        Returns:
            dict: 告警详细信息
        """
        try:
            # 构建请求URL
            url = f"{self.base_url}/skyeye/admin/alert/detail/{alert_id}"

            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': f'{self.base_url}/sensor/alert'
            }

            response = self.session.get(url, headers=headers, verify=False)

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 200:
                    return data.get('data', {})
                else:
                    logger.error(f"获取告警详情失败: {data.get('message', 'Unknown error')}")
                    return None
            else:
                logger.error(f"获取告警详情请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取告警详情异常: {e}")
            return None

    def get_packet_info(self, alert_id):
        """
        获取告警的数据包信息

        Args:
            alert_id (str): 告警ID

        Returns:
            dict: 数据包信息
        """
        try:
            # 构建请求URL
            url = f"{self.base_url}/skyeye/admin/alert/packet/{alert_id}"

            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': f'{self.base_url}/sensor/alert'
            }

            response = self.session.get(url, headers=headers, verify=False)

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 200:
                    return data.get('data', {})
                else:
                    logger.error(f"获取数据包信息失败: {data.get('message', 'Unknown error')}")
                    return None
            else:
                logger.error(f"获取数据包信息请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取数据包信息异常: {e}")
            return None


# 全局变量用于缓存登录状态
_login_manager = None
_alert_manager = None
_detail_manager = None
_last_login_time = None
LOGIN_CACHE_DURATION = 3600  # 登录缓存1小时


def get_authenticated_managers(base_url, username, password):
    """
    获取已认证的管理器实例，如果需要则重新登录

    Args:
        base_url (str): 服务器地址
        username (str): 用户名
        password (str): 密码

    Returns:
        tuple: (login_manager, alert_manager, detail_manager, success)
    """
    global _login_manager, _alert_manager, _detail_manager, _last_login_time

    current_time = time.time()

    # 检查是否需要重新登录
    if (_login_manager is None or _alert_manager is None or _detail_manager is None or
        _last_login_time is None or
        current_time - _last_login_time > LOGIN_CACHE_DURATION):

        logger.info("需要重新登录天眼系统")

        # 创建登录管理器
        login_manager = QianXinLoginSimulator(base_url)
        success = login_manager.login(username, password, auto_recognize=True, verbose=False, max_retries=5)

        if not success:
            logger.error("登录失败，无法继续后续操作")
            return None, None, None, False

        # 创建告警查询管理器和详情管理器
        alert_manager = AlertQueryManager(login_manager)
        detail_manager = AlertDetailManager(login_manager)

        # 更新全局缓存
        _login_manager = login_manager
        _alert_manager = alert_manager
        _detail_manager = detail_manager
        _last_login_time = current_time

        logger.info("登录成功，管理器已缓存")

    return _login_manager, _alert_manager, _detail_manager, True


def unix_timestamp_to_beijing_time(timestamp):
    """
    将unix时间戳转换为北京时间格式

    Args:
        timestamp (int): unix时间戳（秒或毫秒）

    Returns:
        str: 格式化的北京时间字符串，如"7月21日11:30"
    """
    try:
        # 判断是秒还是毫秒时间戳
        if timestamp > 10**10:  # 毫秒时间戳
            timestamp = timestamp / 1000

        # 转换为北京时间 (UTC+8)
        dt = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone(datetime.timedelta(hours=8)))

        # 格式化为"x月x日xx:xx"
        return dt.strftime("%m月%d日%H:%M").lstrip('0').replace('月0', '月')
    except Exception as e:
        logger.error(f"时间转换失败: {e}")
        return str(timestamp)


def map_table_name_to_chinese(table_name):
    """
    将告警类型映射为中文

    Args:
        table_name (str): 原始表名

    Returns:
        str: 中文告警类型
    """
    mapping = {
        'webids_alert': '网页漏洞利用',
        'ips_alert': '网络攻击'
    }
    return mapping.get(table_name, '其他攻击')


def format_alert_data(alerts_data):
    """
    格式化告警数据，只返回重要告警（攻击结果为"成功"或"失陷"，或威胁等级为"危急"）

    Args:
        alerts_data (dict): 原始告警数据

    Returns:
        dict: 格式化后的告警数据
    """
    if not alerts_data or 'items' not in alerts_data:
        return {'total': 0, 'alerts': []}

    formatted_alerts = []

    for item in alerts_data['items']:
        try:
            # 提取所需字段
            access_time = item.get('access_time')
            alarm_sip = item.get('alarm_sip', item.get('dip', 'Unknown'))  # 受害IP (目标IP)
            attack_sip = item.get('attack_sip', item.get('sip', 'Unknown'))  # 攻击IP (源IP)
            table_name = item.get('table_name', 'unknown')
            rule_name = item.get('rule_name', item.get('threat_name', 'Unknown'))
            host_state = item.get('host_state', 'Unknown')  # 攻击结果
            hazard_level = item.get('hazard_level', 'Unknown')  # 威胁级别
            repeat_count = item.get('repeat_count', item.get('count', 1))  # 次数

            # 只返回重要告警
            if host_state not in ["成功", "失陷"] and hazard_level != "危急":
                continue

            # 格式化数据
            formatted_alert = {
                'access_time': unix_timestamp_to_beijing_time(access_time) if access_time else 'Unknown',  # 最近发生时间
                'alarm_sip': alarm_sip,  # 受害IP
                'attack_sip': attack_sip,  # 攻击IP
                'table_name': map_table_name_to_chinese(table_name),  # 告警类型
                'rule_name': rule_name,  # 威胁名称
                'host_state': host_state,  # 攻击结果
                'hazard_level': hazard_level,  # 威胁级别
                'repeat_count': repeat_count  # 次数
            }

            formatted_alerts.append(formatted_alert)

        except Exception as e:
            logger.error(f"格式化告警数据失败: {e}")
            continue

    return {
        'total': len(formatted_alerts),
        'alerts': formatted_alerts
    }


@mcp.tool()
def query_tianyan_alerts(
    minutes_ago: Optional[int] = 5,
    limit: Optional[int] = 50
) -> Dict[str, Any]:
    """查询奇安信天眼平台的告警信息。

    Args:
        minutes_ago: 查询多少分钟前的告警，默认为5分钟
        limit: 返回告警数量限制，默认为50条

    Returns:
        Dict[str, Any]: 包含告警信息的字典
            - status: "success" 或 "error"
            - data: 格式化的告警列表（成功时）
            - message: 错误信息（失败时）
            - total_alerts: 告警总数（成功时）
    """
    # 固定配置
    base_url = "https://*************"
    username = "admin"
    password = "passwd"  # 实际使用加密后的密码

    logger.info(f"[MCP工具] 开始查询天眼告警，参数: minutes_ago={minutes_ago}, limit={limit}")

    try:
        # 确保参数类型正确
        try:
            minutes_ago = int(minutes_ago) if minutes_ago is not None else 5
            limit = int(limit) if limit is not None else 50
        except (ValueError, TypeError) as e:
            return {
                "status": "error",
                "message": f"参数类型错误: minutes_ago和limit必须是数字类型"
            }

        # 获取已认证的管理器
        _, alert_manager, _, success = get_authenticated_managers(base_url, username, password)

        if not success:
            return {
                "status": "error",
                "message": "登录天眼系统失败"
            }

        # 查询告警
        alerts_data = alert_manager.get_alerts_by_minutes_range(
            minutes_ago=minutes_ago,
            limit=limit
        )

        if not alerts_data:
            return {
                "status": "error",
                "message": "查询告警失败，请检查网络连接和权限"
            }

        # 格式化告警数据
        formatted_result = format_alert_data(alerts_data)

        logger.info(f"成功查询天眼告警，共获取到 {formatted_result['total']} 条重要告警")

        return {
            "status": "success",
            "data": formatted_result,
            "total_alerts": formatted_result['total'],
            "message": f"成功查询到 {formatted_result['total']} 条重要告警"
        }

    except Exception as e:
        logger.error(f"查询天眼告警出错: {str(e)}")
        return {
            "status": "error",
            "message": f"查询天眼告警出错: {str(e)}"
        }


def format_alert_detail_data(detail_data, packet_data=None):
    """
    格式化告警详情数据

    Args:
        detail_data (dict): 告警详情数据
        packet_data (dict): 数据包信息（可选）

    Returns:
        dict: 格式化后的告警详情
    """
    if not detail_data:
        return {}

    try:
        formatted_detail = {
            'alert_id': detail_data.get('id', 'Unknown'),
            'alert_name': detail_data.get('rule_name', detail_data.get('threat_name', 'Unknown')),
            'alert_type': map_table_name_to_chinese(detail_data.get('table_name', 'unknown')),
            'source_ip': detail_data.get('attack_sip', detail_data.get('sip', 'Unknown')),
            'target_ip': detail_data.get('alarm_sip', detail_data.get('dip', 'Unknown')),
            'source_port': detail_data.get('sport', 'Unknown'),
            'target_port': detail_data.get('dport', 'Unknown'),
            'protocol': detail_data.get('protocol', 'Unknown'),
            'attack_result': detail_data.get('host_state', 'Unknown'),
            'threat_level': detail_data.get('hazard_level', 'Unknown'),
            'occurrence_time': unix_timestamp_to_beijing_time(detail_data.get('access_time')) if detail_data.get('access_time') else 'Unknown',
            'repeat_count': detail_data.get('repeat_count', detail_data.get('count', 1)),
            'description': detail_data.get('description', ''),
            'solution': detail_data.get('solution', ''),
        }

        # 如果有数据包信息，添加到结果中
        if packet_data:
            formatted_detail['packet_info'] = {
                'request_data': packet_data.get('request', ''),
                'response_data': packet_data.get('response', ''),
                'raw_packet': packet_data.get('raw_data', '')
            }

        return formatted_detail

    except Exception as e:
        logger.error(f"格式化告警详情失败: {e}")
        return {}


@mcp.tool()
def get_tianyan_alert_details(
    alert_id: str,
    base_url: str = "https://*************",
    username: str = "admin",
    password: str = "passwd",
    include_packet_info: Optional[bool] = True
) -> Dict[str, Any]:
    """获取奇安信天眼平台指定告警的详细信息。

    Args:
        alert_id: 告警ID，必需参数
        base_url: 天眼平台地址，默认为https://*************
        username: 登录用户名，默认为admin
        password: 登录密码，默认为passwd
        include_packet_info: 是否包含数据包信息，默认为True

    Returns:
        Dict[str, Any]: 包含告警详细信息的字典
            - status: "success" 或 "error"
            - data: 格式化的告警详情（成功时）
            - message: 错误信息（失败时）
    """
    logger.info(f"[MCP工具] 开始获取告警详情，alert_id={alert_id}, base_url={base_url}, username={username}")

    try:
        # 参数验证
        if not alert_id or not alert_id.strip():
            return {
                "status": "error",
                "message": "告警ID不能为空"
            }

        if not base_url or not base_url.strip():
            return {
                "status": "error",
                "message": "天眼平台地址不能为空"
            }

        if not username or not username.strip():
            return {
                "status": "error",
                "message": "用户名不能为空"
            }

        # 获取已认证的管理器
        _, _, detail_manager, success = get_authenticated_managers(base_url, username, password)

        if not success:
            return {
                "status": "error",
                "message": "登录天眼系统失败，请检查地址、用户名和密码"
            }

        # 获取告警详情
        detail_data = detail_manager.get_alert_detail(alert_id)

        if not detail_data:
            return {
                "status": "error",
                "message": f"获取告警 {alert_id} 的详细信息失败，可能告警ID不存在"
            }

        # 获取数据包信息（如果需要）
        packet_data = None
        if include_packet_info:
            packet_data = detail_manager.get_packet_info(alert_id)
            if not packet_data:
                logger.warning(f"获取告警 {alert_id} 的数据包信息失败，但继续返回基本详情")

        # 格式化详情数据
        formatted_detail = format_alert_detail_data(detail_data, packet_data)

        if not formatted_detail:
            return {
                "status": "error",
                "message": "格式化告警详情失败"
            }

        logger.info(f"成功获取告警 {alert_id} 的详细信息")

        return {
            "status": "success",
            "data": formatted_detail,
            "message": f"成功获取告警 {alert_id} 的详细信息"
        }

    except Exception as e:
        logger.error(f"获取告警详情出错: {str(e)}")
        return {
            "status": "error",
            "message": f"获取告警详情出错: {str(e)}"
        }


if __name__ == "__main__":
    logger.info("启动奇安信天眼告警服务MCP服务器")
    mcp.run(transport="stdio")
